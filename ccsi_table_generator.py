#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CCSI评分表生成器
基于DAI、HE、结肠长度三个指标生成详细的CCSI评分表
"""

import pandas as pd
import numpy as np

class CCSITableGenerator:
    """CCSI评分表生成器"""
    
    def __init__(self, data_path):
        self.data_path = data_path
        self.data = None
        self.load_data()
    
    def load_data(self):
        """加载数据"""
        try:
            if self.data_path.endswith('.csv'):
                self.data = pd.read_csv(self.data_path, encoding='utf-8')
            else:
                self.data = pd.read_excel(self.data_path, sheet_name=0)
            print(f"✓ 成功加载数据：{len(self.data)}条记录")
        except Exception as e:
            print(f"✗ 数据加载失败：{e}")
    
    def calculate_dai_score(self, dai_value):
        """
        计算DAI得分 (0-10范围)
        DAI范围通常0-7，转换为0-10
        """
        if pd.isna(dai_value):
            return 0
        # DAI标准化：0-7 -> 0-10
        return min(dai_value / 7.0 * 10, 10)
    
    def calculate_he_score(self, he_value):
        """
        计算HE得分 (0-10范围)
        HE范围通常0-9，转换为0-10
        """
        if pd.isna(he_value):
            return 0
        # HE标准化：0-9 -> 0-10
        return min(he_value / 9.0 * 10, 10)
    
    def calculate_colon_score(self, colon_length):
        """
        计算结肠长度得分 (0-10范围)
        正常结肠长度约8.5cm，炎症时缩短，长度越短得分越高
        """
        if pd.isna(colon_length):
            return 0
        
        # 设定评分标准
        normal_length = 8.5  # 正常长度
        severe_length = 5.0  # 严重炎症长度
        
        if colon_length >= normal_length:
            return 0  # 正常或更长，无炎症
        elif colon_length <= severe_length:
            return 10  # 严重缩短，最高分
        else:
            # 线性插值
            score = (normal_length - colon_length) / (normal_length - severe_length) * 10
            return min(score, 10)
    
    def calculate_ccsi_equal_weight(self, dai_score, he_score, colon_score):
        """
        计算等权重CCSI综合得分
        三个指标等权重平均
        """
        valid_scores = [score for score in [dai_score, he_score, colon_score] if not pd.isna(score)]
        if valid_scores:
            return np.mean(valid_scores)
        return 0
    
    def generate_ccsi_table(self):
        """生成CCSI评分表"""
        if self.data is None:
            print("请先加载数据")
            return None
        
        # 按组别计算统计量
        group_stats = self.data.groupby('group').agg({
            'DAI': 'mean',
            'HE': 'mean', 
            '结肠长度': 'mean'
        }).reset_index()
        
        # 计算各项得分
        results = []
        for _, row in group_stats.iterrows():
            group = row['group']
            dai_mean = row['DAI']
            he_mean = row['HE']
            colon_mean = row['结肠长度']
            
            # 计算各项得分
            dai_score = self.calculate_dai_score(dai_mean)
            he_score = self.calculate_he_score(he_mean)
            colon_score = self.calculate_colon_score(colon_mean)
            
            # 计算综合得分
            ccsi_equal = self.calculate_ccsi_equal_weight(dai_score, he_score, colon_score)
            
            results.append({
                'Group': group,
                'DAI_score': round(dai_score, 3),
                'HE_score': round(he_score, 3),
                'Colon_score': round(colon_score, 3),
                'CCSI_equal': round(ccsi_equal, 3)
            })
        
        # 创建DataFrame
        ccsi_table = pd.DataFrame(results)
        
        # 按CCSI得分排序
        ccsi_table = ccsi_table.sort_values('CCSI_equal')
        
        return ccsi_table
    
    def format_table_for_display(self, ccsi_table):
        """格式化表格用于显示"""
        if ccsi_table is None:
            return None
        
        # 创建格式化的表格
        formatted_table = ccsi_table.copy()
        
        # 重命名列以匹配图片格式
        formatted_table.columns = ['Group', 'DAI_score', 'HE_score', 'Colon_score', 'CCSI_equal']
        
        return formatted_table
    
    def save_table(self, ccsi_table, output_path='ccsi_scores_table.csv'):
        """保存表格到文件"""
        if ccsi_table is not None:
            ccsi_table.to_csv(output_path, index=False, encoding='utf-8')
            print(f"✓ CCSI评分表已保存至：{output_path}")
    
    def print_formatted_table(self, ccsi_table):
        """打印格式化的表格"""
        if ccsi_table is None:
            return
        
        print("\n" + "="*70)
        print("表 CCSI评分")
        print("="*70)
        
        # 打印表头
        print(f"{'Group':<12} {'DAI_score':<10} {'HE_score':<10} {'Colon_score':<12} {'CCSI_equal':<10}")
        print("-" * 70)
        
        # 打印数据行
        for _, row in ccsi_table.iterrows():
            group = row['Group']
            # 标记特定组别（如图片中红色标记的）
            if any(marker in str(group) for marker in ['Meb1-S1', 'Meb1-S2', 'Meb1-S5', 'Meb1-S7', 'Meb2']):
                marker = "*"  # 用星号标记重要组别
            else:
                marker = " "
            
            print(f"{marker}{group:<11} {row['DAI_score']:<10.3f} {row['HE_score']:<10.3f} {row['Colon_score']:<12.3f} {row['CCSI_equal']:<10.3f}")

def main():
    """主函数"""
    # 创建CCSI表生成器
    generator = CCSITableGenerator('03- 表型指标全数据_2.csv')
    
    # 生成CCSI评分表
    ccsi_table = generator.generate_ccsi_table()
    
    if ccsi_table is not None:
        # 打印格式化表格
        generator.print_formatted_table(ccsi_table)
        
        # 保存表格
        generator.save_table(ccsi_table)
        
        # 生成详细分析
        print(f"\n📊 CCSI评分分析:")
        print(f"最佳组别: {ccsi_table.iloc[0]['Group']} (CCSI={ccsi_table.iloc[0]['CCSI_equal']:.3f})")
        print(f"最差组别: {ccsi_table.iloc[-1]['Group']} (CCSI={ccsi_table.iloc[-1]['CCSI_equal']:.3f})")
        
        # 找出CCSI < 5.0的优秀组别
        excellent_groups = ccsi_table[ccsi_table['CCSI_equal'] < 5.0]
        if not excellent_groups.empty:
            print(f"\n🏆 优秀组别 (CCSI < 5.0):")
            for _, row in excellent_groups.iterrows():
                print(f"  {row['Group']}: {row['CCSI_equal']:.3f}")
    
    print("\n✓ CCSI评分表生成完成！")

if __name__ == "__main__":
    main()