#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
结肠炎实验数据分析脚本
处理DAI、结肠长度、HE指标并进行统计分析
"""

import pandas as pd
import numpy as np
from scipy import stats
from scipy.stats import f_oneway, ttest_ind
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class ColitisAnalyzer:
    """结肠炎数据分析器"""
    
    def __init__(self, data_path):
        """
        初始化分析器
        
        Args:
            data_path (str): 数据文件路径
        """
        self.data_path = data_path
        self.df = None
        self.reference_group = 'con'  # 参考组设为con
        self.indicators = ['DAI', '结肠长度', 'HE']  # 三个指标
        
    def load_data(self):
        """加载数据"""
        self.df = pd.read_csv(self.data_path, encoding='utf-8')
        print(f"数据加载完成，共{len(self.df)}个样本")
        print(f"分组情况：\n{self.df['group'].value_counts()}")
        return self.df
    
    def calculate_group_statistics(self):
        """
        计算每组的统计值（平均值）
        
        Returns:
            pandas.DataFrame: 包含每组统计值的数据框
        """
        group_stats = []
        
        for group in self.df['group'].unique():
            group_data = self.df[self.df['group'] == group]
            
            stats = {'Group': group}
            for indicator in self.indicators:
                values = group_data[indicator].dropna()
                if len(values) > 0:
                    stats[f'{indicator}_mean'] = values.mean()
                    stats[f'{indicator}_std'] = values.std()
                    stats[f'{indicator}_count'] = len(values)
                else:
                    stats[f'{indicator}_mean'] = np.nan
                    stats[f'{indicator}_std'] = np.nan
                    stats[f'{indicator}_count'] = 0
        
            group_stats.append(stats)
        
        return pd.DataFrame(group_stats)
    
    def calculate_group_level_rei(self):
        """
        基于组水平统计值计算相对疗效指数（REI）
        
        Returns:
            pandas.DataFrame: 包含组水平REI分数的数据框
        """
        group_stats = self.calculate_group_statistics()
        
        # 获取参考组的均值
        ref_row = group_stats[group_stats['Group'] == self.reference_group]
        if len(ref_row) == 0:
            raise ValueError(f"未找到参考组 {self.reference_group}")
        
        result_df = group_stats[['Group']].copy()
        
        for indicator in self.indicators:
            ref_mean = ref_row[f'{indicator}_mean'].iloc[0]
            
            if pd.notna(ref_mean) and ref_mean != 0:
                if indicator == '结肠长度':
                    # 结肠长度：值越大越好
                    result_df[f'{indicator}_REI'] = (group_stats[f'{indicator}_mean'] - ref_mean) / ref_mean * 100
                else:
                    # DAI和HE：值越小越好
                    result_df[f'{indicator}_REI'] = (ref_mean - group_stats[f'{indicator}_mean']) / ref_mean * 100
            else:
                result_df[f'{indicator}_REI'] = np.nan
        
        # 计算综合REI
        rei_cols = [f'{ind}_REI' for ind in self.indicators]
        result_df['综合_REI'] = result_df[rei_cols].mean(axis=1)
        
        return result_df
    
    def calculate_group_level_cohens_d(self):
        """
        基于组水平统计值计算Cohen's d效应量
        
        Returns:
            pandas.DataFrame: 包含组水平Cohen's d分数的数据框
        """
        group_stats = self.calculate_group_statistics()
        
        # 获取参考组的统计值
        ref_row = group_stats[group_stats['Group'] == self.reference_group]
        if len(ref_row) == 0:
            raise ValueError(f"未找到参考组 {self.reference_group}")
        
        result_df = group_stats[['Group']].copy()
        
        for indicator in self.indicators:
            ref_mean = ref_row[f'{indicator}_mean'].iloc[0]
            ref_std = ref_row[f'{indicator}_std'].iloc[0]
            
            if pd.notna(ref_mean) and pd.notna(ref_std) and ref_std > 0:
                # 使用参考组的标准差计算效应量
                result_df[f'{indicator}_Cohens_d'] = (group_stats[f'{indicator}_mean'] - ref_mean) / ref_std
            else:
                result_df[f'{indicator}_Cohens_d'] = np.nan
        
        # 计算综合Cohen's d（绝对值的平均）
        cohens_cols = [f'{ind}_Cohens_d' for ind in self.indicators]
        result_df['综合_Cohens_d'] = result_df[cohens_cols].abs().mean(axis=1)
        
        return result_df
    
    def calculate_group_level_zscore(self):
        """
        基于组水平统计值计算Z-score标准化
        
        Returns:
            pandas.DataFrame: 包含组水平Z-score分数的数据框
        """
        group_stats = self.calculate_group_statistics()
        result_df = group_stats[['Group']].copy()
        
        for indicator in self.indicators:
            values = group_stats[f'{indicator}_mean'].dropna()
            if len(values) > 1:
                mean_val = values.mean()
                std_val = values.std()
                if std_val > 0:
                    # Z-score标准化，转换为均值50，标准差10的分数
                    result_df[f'{indicator}_Zscore'] = ((group_stats[f'{indicator}_mean'] - mean_val) / std_val) * 10 + 50
                else:
                    result_df[f'{indicator}_Zscore'] = 50  # 如果标准差为0，设为均值
            else:
                result_df[f'{indicator}_Zscore'] = np.nan
        
        # 计算综合Z-score
        zscore_cols = [f'{ind}_Zscore' for ind in self.indicators]
        result_df['综合_Zscore'] = result_df[zscore_cols].mean(axis=1)
        
        return result_df
    
    def calculate_group_level_ccsi(self):
        """
        基于组水平统计值计算CCSI综合评分
        
        Returns:
            pandas.DataFrame: 包含组水平CCSI分数的数据框
        """
        group_stats = self.calculate_group_statistics()
        
        # 获取参考组的统计值
        ref_row = group_stats[group_stats['Group'] == self.reference_group]
        if len(ref_row) == 0:
            raise ValueError(f"未找到参考组 {self.reference_group}")
        
        # 设置权重
        weights = {'DAI': 0.4, '结肠长度': 0.3, 'HE': 0.3}
        
        result_df = group_stats[['Group']].copy()
        
        for indicator in self.indicators:
            ref_mean = ref_row[f'{indicator}_mean'].iloc[0]
            ref_std = ref_row[f'{indicator}_std'].iloc[0]
            
            if pd.notna(ref_mean) and pd.notna(ref_std) and ref_std > 0:
                if indicator == '结肠长度':
                    # 结肠长度：值越大越好
                    result_df[f'{indicator}_CCSI'] = (group_stats[f'{indicator}_mean'] - ref_mean) / ref_std
                else:
                    # DAI和HE：值越小越好
                    result_df[f'{indicator}_CCSI'] = -(group_stats[f'{indicator}_mean'] - ref_mean) / ref_std
            else:
                result_df[f'{indicator}_CCSI'] = 0
        
        # 计算加权CCSI综合评分
        result_df['综合_CCSI'] = (
            weights['DAI'] * result_df['DAI_CCSI'] +
            weights['结肠长度'] * result_df['结肠长度_CCSI'] +
            weights['HE'] * result_df['HE_CCSI']
        )
        
        return result_df
    
    def generate_all_analyses(self):
        """
        生成所有四种分析方法的结果
        
        Returns:
            dict: 包含所有分析结果的字典
        """
        analyses = {}
        
        print("正在计算相对疗效指数（REI）...")
        analyses['REI'] = self.calculate_rei()
        
        print("正在计算Cohen's d效应量...")
        analyses['Cohens_d'] = self.calculate_cohens_d()
        
        print("正在计算Z-score标准化...")
        analyses['Zscore'] = self.calculate_zscore()
        
        print("正在计算CCSI综合评分...")
        analyses['CCSI'] = self.calculate_ccsi()
        
        return analyses
    
    def save_analysis_results(self, analyses, output_dir='analysis_results'):
        """
        保存所有分析结果到单独的文件
        
        Args:
            analyses (dict): 分析结果字典
            output_dir (str): 输出目录
        """
        Path(output_dir).mkdir(exist_ok=True)
        
        for method_name, df in analyses.items():
            filename = f"{output_dir}/colitis_analysis_{method_name}.csv"
            df.to_csv(filename, index=False, encoding='utf-8')
            print(f"{method_name}分析结果已保存至：{filename}")
    
    def generate_summary_statistics(self, analyses):
        """
        生成各种方法的统计摘要
        
        Args:
            analyses (dict): 分析结果字典
            
        Returns:
            dict: 统计摘要
        """
        summaries = {}
        
        for method_name, df in analyses.items():
            summaries[method_name] = {}
            
            # 获取该方法的评分列
            if method_name == 'REI':
                score_cols = [f'{ind}_REI' for ind in self.indicators] + ['综合_REI']
            elif method_name == 'Cohens_d':
                score_cols = [f'{ind}_Cohens_d' for ind in self.indicators] + ['综合_Cohens_d']
            elif method_name == 'Zscore':
                score_cols = [f'{ind}_Zscore' for ind in self.indicators] + ['综合_Zscore']
            elif method_name == 'CCSI':
                score_cols = [f'{ind}_CCSI' for ind in self.indicators] + ['综合_CCSI']
            
            # 按组计算统计摘要
            for group in df['group'].unique():
                group_data = df[df['group'] == group]
                summaries[method_name][group] = {}
                
                for col in score_cols:
                    if col in df.columns:
                        values = group_data[col].dropna()
                        if len(values) > 0:
                            summaries[method_name][group][col] = {
                                'mean': values.mean(),
                                'std': values.std(),
                                'count': len(values),
                                'min': values.min(),
                                'max': values.max()
                            }
        
        return summaries
    
    def _cohens_d(self, group1, group2):
        """计算Cohen's d效应大小"""
        n1, n2 = len(group1), len(group2)
        pooled_std = np.sqrt(((n1 - 1) * np.var(group1, ddof=1) + 
                            (n2 - 1) * np.var(group2, ddof=1)) / (n1 + n2 - 2))
        return (np.mean(group1) - np.mean(group2)) / pooled_std
    
def main():
    """主函数"""
    print("=" * 60)
    print("结肠炎实验数据多方法分析")
    print("=" * 60)
    
    # 初始化分析器
    analyzer = ColitisAnalyzer('03- 表型指标全数据_2.csv')
    
    # 加载数据
    data = analyzer.load_data()
    
    # 生成所有分析方法的结果
    analyses = analyzer.generate_all_analyses()
    
    # 生成统计摘要
    print("\n正在生成统计摘要...")
    summaries = analyzer.generate_summary_statistics(analyses)
    
    # 保存分析结果到单独文件
    print("\n正在保存分析结果...")
    analyzer.save_analysis_results(analyses)
    
    # 打印各方法的简要统计
    print("\n" + "=" * 60)
    print("各方法统计摘要（以con组为参考）")
    print("=" * 60)
    
    for method_name, method_summary in summaries.items():
        print(f"\n{method_name}方法:")
        print("-" * 30)
        
        for group, group_stats in method_summary.items():
            if group == analyzer.reference_group:
                continue  # 跳过参考组
                
            print(f"\n{group}组 vs {analyzer.reference_group}组:")
            
            for score_name, stats in group_stats.items():
                if '综合' in score_name:
                    print(f"  {score_name}: {stats['mean']:.2f} ± {stats['std']:.2f} (n={stats['count']})")
    
    print(f"\n所有分析完成！")
    print(f"参考组：{analyzer.reference_group}")
    print(f"分析指标：{', '.join(analyzer.indicators)}")
    print(f"分析方法：REI, Cohen's d, Z-score, CCSI")
    
    return analyzer, analyses, summaries

if __name__ == "__main__":
    analyzer, analyses, summaries = main()