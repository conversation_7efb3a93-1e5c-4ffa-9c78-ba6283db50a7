#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
详细指标评分表生成器
分别生成DAI、HE、结肠长度的详细评分表
"""

import pandas as pd
import numpy as np

class DetailedScoreTableGenerator:
    """详细评分表生成器"""
    
    def __init__(self, data_path):
        self.data_path = data_path
        self.data = None
        self.load_data()
    
    def load_data(self):
        """加载数据"""
        try:
            if self.data_path.endswith('.csv'):
                self.data = pd.read_csv(self.data_path, encoding='utf-8')
            else:
                self.data = pd.read_excel(self.data_path, sheet_name=0)
            print(f"✓ 成功加载数据：{len(self.data)}条记录")
        except Exception as e:
            print(f"✗ 数据加载失败：{e}")
    
    def generate_all_indicator_tables(self):
        """生成所有指标的详细评分表"""
        if self.data is None:
            return None
        
        # 按组别计算统计量
        group_stats = self.data.groupby('group').agg({
            'DAI': ['count', 'mean', 'std', 'sem'],
            'HE': ['count', 'mean', 'std', 'sem'],
            '结肠长度': ['count', 'mean', 'std', 'sem']
        }).reset_index()
        
        # 扁平化列名
        group_stats.columns = ['group'] + [f'{col[0]}_{col[1]}' for col in group_stats.columns[1:]]
        
        results = []
        for _, row in group_stats.iterrows():
            group = row['group']
            
            # DAI指标
            dai_mean = row.get('DAI_mean', 0)
            dai_std = row.get('DAI_std', 0)
            dai_sem = row.get('DAI_sem', 0)
            dai_count = row.get('DAI_count', 0)
            dai_score = self.calculate_dai_score(dai_mean)
            
            # HE指标
            he_mean = row.get('HE_mean', 0) if not pd.isna(row.get('HE_mean')) else 0
            he_std = row.get('HE_std', 0) if not pd.isna(row.get('HE_std')) else 0
            he_sem = row.get('HE_sem', 0) if not pd.isna(row.get('HE_sem')) else 0
            he_count = row.get('HE_count', 0)
            he_score = self.calculate_he_score(he_mean)
            
            # 结肠长度指标
            colon_mean = row.get('结肠长度_mean', 0)
            colon_std = row.get('结肠长度_std', 0)
            colon_sem = row.get('结肠长度_sem', 0)
            colon_count = row.get('结肠长度_count', 0)
            colon_score = self.calculate_colon_score(colon_mean)
            
            # 综合评分
            ccsi_equal = self.calculate_ccsi_equal_weight(dai_score, he_score, colon_score)
            
            results.append({
                'Group': group,
                'DAI_mean': round(dai_mean, 2),
                'DAI_std': round(dai_std, 2),
                'DAI_sem': round(dai_sem, 2),
                'DAI_count': int(dai_count),
                'DAI_score': round(dai_score, 3),
                'HE_mean': round(he_mean, 2),
                'HE_std': round(he_std, 2), 
                'HE_sem': round(he_sem, 2),
                'HE_count': int(he_count),
                'HE_score': round(he_score, 3),
                'Colon_mean': round(colon_mean, 2),
                'Colon_std': round(colon_std, 2),
                'Colon_sem': round(colon_sem, 2),
                'Colon_count': int(colon_count),
                'Colon_score': round(colon_score, 3),
                'CCSI_equal': round(ccsi_equal, 3)
            })
        
        return pd.DataFrame(results)
    
    def calculate_dai_score(self, dai_value):
        """计算DAI得分 (0-10范围)"""
        if pd.isna(dai_value):
            return 0
        return min(dai_value / 7.0 * 10, 10)
    
    def calculate_he_score(self, he_value):
        """计算HE得分 (0-10范围)"""
        if pd.isna(he_value):
            return 0
        return min(he_value / 9.0 * 10, 10)
    
    def calculate_colon_score(self, colon_length):
        """计算结肠长度得分 (0-10范围)"""
        if pd.isna(colon_length):
            return 0
        
        normal_length = 8.5
        severe_length = 5.0
        
        if colon_length >= normal_length:
            return 0
        elif colon_length <= severe_length:
            return 10
        else:
            score = (normal_length - colon_length) / (normal_length - severe_length) * 10
            return min(score, 10)
    
    def calculate_ccsi_equal_weight(self, dai_score, he_score, colon_score):
        """计算等权重CCSI综合得分"""
        valid_scores = [score for score in [dai_score, he_score, colon_score] if not pd.isna(score)]
        if valid_scores:
            return np.mean(valid_scores)
        return 0
    
    def print_dai_table(self, detailed_table):
        """打印DAI指标表"""
        print("\n" + "="*80)
        print("表1 DAI指标评分")
        print("="*80)
        print(f"{'Group':<10} {'Mean':<6} {'Std':<6} {'SEM':<6} {'Count':<6} {'DAI_Score':<10}")
        print("-" * 80)
        
        for _, row in detailed_table.iterrows():
            print(f"{row['Group']:<10} {row['DAI_mean']:<6} {row['DAI_std']:<6} {row['DAI_sem']:<6} {row['DAI_count']:<6} {row['DAI_score']:<10.3f}")
    
    def print_he_table(self, detailed_table):
        """打印HE指标表"""
        print("\n" + "="*80)
        print("表2 HE指标评分")
        print("="*80)
        print(f"{'Group':<10} {'Mean':<6} {'Std':<6} {'SEM':<6} {'Count':<6} {'HE_Score':<10}")
        print("-" * 80)
        
        for _, row in detailed_table.iterrows():
            print(f"{row['Group']:<10} {row['HE_mean']:<6} {row['HE_std']:<6} {row['HE_sem']:<6} {row['HE_count']:<6} {row['HE_score']:<10.3f}")
    
    def print_colon_table(self, detailed_table):
        """打印结肠长度指标表"""
        print("\n" + "="*80)
        print("表3 结肠长度指标评分")
        print("="*80)
        print(f"{'Group':<10} {'Mean':<6} {'Std':<6} {'SEM':<6} {'Count':<6} {'Colon_Score':<12}")
        print("-" * 80)
        
        for _, row in detailed_table.iterrows():
            print(f"{row['Group']:<10} {row['Colon_mean']:<6} {row['Colon_std']:<6} {row['Colon_sem']:<6} {row['Colon_count']:<6} {row['Colon_score']:<12.3f}")
    
    def print_summary_table(self, detailed_table):
        """打印综合评分表"""
        print("\n" + "="*70)
        print("表4 CCSI综合评分")
        print("="*70)
        print(f"{'Group':<12} {'DAI_score':<10} {'HE_score':<10} {'Colon_score':<12} {'CCSI_equal':<10}")
        print("-" * 70)
        
        # 按CCSI得分排序
        sorted_table = detailed_table.sort_values('CCSI_equal')
        
        for _, row in sorted_table.iterrows():
            print(f"{row['Group']:<12} {row['DAI_score']:<10.3f} {row['HE_score']:<10.3f} {row['Colon_score']:<12.3f} {row['CCSI_equal']:<10.3f}")
    
    def save_detailed_tables(self, detailed_table):
        """保存详细表格"""
        if detailed_table is not None:
            # 保存完整表格
            detailed_table.to_csv('detailed_scores_all_indicators.csv', index=False, encoding='utf-8')
            
            # 保存单独的指标表
            dai_table = detailed_table[['Group', 'DAI_mean', 'DAI_std', 'DAI_sem', 'DAI_count', 'DAI_score']]
            dai_table.to_csv('dai_scores_table.csv', index=False, encoding='utf-8')
            
            he_table = detailed_table[['Group', 'HE_mean', 'HE_std', 'HE_sem', 'HE_count', 'HE_score']]
            he_table.to_csv('he_scores_table.csv', index=False, encoding='utf-8')
            
            colon_table = detailed_table[['Group', 'Colon_mean', 'Colon_std', 'Colon_sem', 'Colon_count', 'Colon_score']]
            colon_table.to_csv('colon_scores_table.csv', index=False, encoding='utf-8')
            
            ccsi_table = detailed_table[['Group', 'DAI_score', 'HE_score', 'Colon_score', 'CCSI_equal']].sort_values('CCSI_equal')
            ccsi_table.to_csv('ccsi_summary_table.csv', index=False, encoding='utf-8')
            
            print(f"\n✓ 已保存以下表格文件：")
            print(f"  - 完整详细表格: detailed_scores_all_indicators.csv")
            print(f"  - DAI评分表: dai_scores_table.csv")
            print(f"  - HE评分表: he_scores_table.csv")
            print(f"  - 结肠长度评分表: colon_scores_table.csv")
            print(f"  - CCSI综合评分表: ccsi_summary_table.csv")

def main():
    """主函数"""
    generator = DetailedScoreTableGenerator('03- 表型指标全数据_2.csv')
    
    # 生成详细评分表
    detailed_table = generator.generate_all_indicator_tables()
    
    if detailed_table is not None:
        # 打印各个指标表
        generator.print_dai_table(detailed_table)
        generator.print_he_table(detailed_table)
        generator.print_colon_table(detailed_table)
        generator.print_summary_table(detailed_table)
        
        # 保存表格文件
        generator.save_detailed_tables(detailed_table)
    
    print("\n✓ 详细指标评分表生成完成！")

if __name__ == "__main__":
    main()