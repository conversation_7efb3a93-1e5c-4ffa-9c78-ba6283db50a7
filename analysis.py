#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
溃疡性结肠炎治疗效果综合评分分析系统
Comprehensive Scoring System for Ulcerative Colitis Treatment Evaluation

作者：AI Assistant
版本：1.0
日期：2024
"""

import pandas as pd
import numpy as np
from scipy import stats
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, List, Tuple
import warnings
warnings.filterwarnings('ignore')

# 设置中文显示 - 使用macOS系统字体
plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class ColitisScoring:
    """溃疡性结肠炎评分系统"""
    
    def __init__(self, data_path: str = None):
        """
        初始化评分系统
        
        Parameters:
        -----------
        data_path : str
            Excel数据文件路径
        """
        self.data = None
        self.group_stats = {}
        self.rei_scores = {}
        self.smd_scores = {}
        
        # 标准权重设置（每个指标权重相等）
        self.weights = {
            'HE': 1/3,    # HE病理评分权重33.3%
            'DAI': 1/3,   # DAI临床指数权重33.3%
            'Colon': 1/3  # 结肠长度权重33.3%
        }
        
        if data_path:
            self.load_data(data_path)
    
    def load_data(self, filepath: str):
        """加载Excel数据"""
        try:
            self.data = pd.read_excel(filepath, sheet_name=0)
            print(f"✓ 成功加载数据：{len(self.data)}条记录")
            self._calculate_group_stats()
        except Exception as e:
            print(f"✗ 数据加载失败：{e}")
    
    def _calculate_group_stats(self):
        """计算各组统计量"""
        if self.data is None:
            return
        
        groups = self.data['group'].unique()
        
        for group in groups:
            group_data = self.data[self.data['group'] == group]
            
            self.group_stats[group] = {
                'n': len(group_data),
                'DAI': {
                    'mean': group_data['DAI'].mean(),
                    'std': group_data['DAI'].std(),
                    'sem': group_data['DAI'].sem()
                },
                '结肠长度': {
                    'mean': group_data['结肠长度'].mean(),
                    'std': group_data['结肠长度'].std(),
                    'sem': group_data['结肠长度'].sem()
                },
                'HE': {
                    'mean': group_data['HE'].mean() if 'HE' in group_data.columns else np.nan,
                    'std': group_data['HE'].std() if 'HE' in group_data.columns else np.nan,
                    'sem': group_data['HE'].sem() if 'HE' in group_data.columns else np.nan
                }
            }
    
    def calculate_individual_scores(self):
        """
        计算每个样本的单项得分和综合得分
        
        Returns:
        --------
        DataFrame : 包含原始数据和所有得分的数据框
        """
        if self.data is None:
            print("请先加载数据")
            return None
        
        # 复制原始数据
        scored_data = self.data.copy()
        
        # 获取各指标的最大最小值用于标准化
        dai_max = scored_data['DAI'].max()
        dai_min = scored_data['DAI'].min()
        colon_max = scored_data['结肠长度'].max() 
        colon_min = scored_data['结肠长度'].min()
        he_max = scored_data['HE'].max()
        he_min = scored_data['HE'].min()
        
        # 计算DAI得分 (0-100，值越大炎症越严重)
        if dai_max > dai_min:
            scored_data['DAI得分'] = ((scored_data['DAI'] - dai_min) / (dai_max - dai_min)) * 100
        else:
            scored_data['DAI得分'] = 0
            
        # 计算结肠长度得分 (0-100，长度越短炎症越严重)
        if colon_max > colon_min:
            scored_data['结肠长度得分'] = (1 - (scored_data['结肠长度'] - colon_min) / (colon_max - colon_min)) * 100
        else:
            scored_data['结肠长度得分'] = 0
            
        # 计算HE得分 (0-100，值越大炎症越严重)
        if he_max > he_min and not np.isnan(he_max):
            scored_data['HE得分'] = ((scored_data['HE'] - he_min) / (he_max - he_min)) * 100
        else:
            scored_data['HE得分'] = np.nan
        
        # 计算综合得分 (只对有完整数据的样本计算)
        scored_data['综合得分'] = np.nan
        
        # 对有完整数据的行计算综合得分
        complete_mask = ~scored_data[['DAI', '结肠长度', 'HE']].isna().any(axis=1)
        if complete_mask.sum() > 0:
            scored_data.loc[complete_mask, '综合得分'] = (
                self.weights['DAI'] * scored_data.loc[complete_mask, 'DAI得分'] +
                self.weights['Colon'] * scored_data.loc[complete_mask, '结肠长度得分'] +
                self.weights['HE'] * scored_data.loc[complete_mask, 'HE得分']
            )
        
        print(f"✓ 个体得分计算完成")
        print(f"✓ 权重设置：DAI={self.weights['DAI']:.3f}, 结肠长度={self.weights['Colon']:.3f}, HE={self.weights['HE']:.3f}")
        print(f"✓ 有完整数据的样本数：{complete_mask.sum()}/{len(scored_data)}")
        
        return scored_data
    
    def calculate_rei(self, disease_group: str = 'D', standard_group: str = '5A'):
        """
        计算相对疗效指数 (Relative Efficacy Index)
        
        Parameters:
        -----------
        disease_group : str
            疾病对照组名称
        standard_group : str
            金标准药物组名称
        
        Returns:
        --------
        DataFrame : REI评分结果
        """
        if not self.group_stats:
            print("请先加载数据")
            return None
        
        # 获取疾病组和标准组数据
        d_stats = self.group_stats[disease_group]
        s_stats = self.group_stats[standard_group]
        
        results = []
        
        for group in self.group_stats.keys():
            g_stats = self.group_stats[group]  # 移动到循环开始，确保所有组都能访问
            
            if group == disease_group:
                rei = 0
            elif group == 'con':
                rei = None  # 正常对照组不计算REI
            else:
                # DAI改善率（值越小越好）
                dai_imp = (d_stats['DAI']['mean'] - g_stats['DAI']['mean']) / \
                         (d_stats['DAI']['mean'] - s_stats['DAI']['mean']) * 100
                
                # 结肠长度改善率（值越大越好）
                colon_imp = (g_stats['结肠长度']['mean'] - d_stats['结肠长度']['mean']) / \
                           (s_stats['结肠长度']['mean'] - d_stats['结肠长度']['mean']) * 100
                
                # HE改善率（值越小越好）
                if not np.isnan(g_stats['HE']['mean']):
                    he_imp = (d_stats['HE']['mean'] - g_stats['HE']['mean']) / \
                            (d_stats['HE']['mean'] - s_stats['HE']['mean']) * 100
                else:
                    he_imp = dai_imp  # 如无HE数据，用DAI替代
                
                # 加权综合REI
                rei = (self.weights['DAI'] * dai_imp + 
                      self.weights['Colon'] * colon_imp + 
                      self.weights['HE'] * he_imp)
                
                self.rei_scores[group] = {
                    'DAI_improvement': dai_imp,
                    'Colon_improvement': colon_imp,
                    'HE_improvement': he_imp,
                    'REI': rei
                }
            
            results.append({
                '组别': group,
                'DAI均值': f"{g_stats['DAI']['mean']:.2f}±{g_stats['DAI']['sem']:.2f}",
                '结肠长度': f"{g_stats['结肠长度']['mean']:.2f}±{g_stats['结肠长度']['sem']:.2f}",
                'HE评分': f"{g_stats['HE']['mean']:.2f}±{g_stats['HE']['sem']:.2f}" 
                         if not np.isnan(g_stats['HE']['mean']) else 'N/A',
                'REI(%)': f"{rei:.1f}" if rei is not None else 'N/A'
            })
        
        df_results = pd.DataFrame(results)
        df_results = df_results.sort_values('REI(%)', ascending=False, na_position='last')
        
        return df_results
    
    def calculate_smd(self, disease_group: str = 'D'):
        """
        计算标准化均值差 (Standardized Mean Difference, Cohen's d)
        
        Parameters:
        -----------
        disease_group : str
            疾病对照组名称
        
        Returns:
        --------
        DataFrame : SMD评分结果
        """
        if not self.group_stats:
            print("请先加载数据")
            return None
        
        d_stats = self.group_stats[disease_group]
        results = []
        
        for group in self.group_stats.keys():
            if group == disease_group:
                smd_total = 0
            else:
                g_stats = self.group_stats[group]
                
                # 计算Cohen's d
                # DAI (负值表示改善)
                pooled_sd_dai = np.sqrt((d_stats['DAI']['std']**2 + g_stats['DAI']['std']**2) / 2)
                dai_d = -(g_stats['DAI']['mean'] - d_stats['DAI']['mean']) / pooled_sd_dai
                
                # 结肠长度 (正值表示改善)
                pooled_sd_colon = np.sqrt((d_stats['结肠长度']['std']**2 + 
                                          g_stats['结肠长度']['std']**2) / 2)
                colon_d = (g_stats['结肠长度']['mean'] - d_stats['结肠长度']['mean']) / pooled_sd_colon
                
                # HE (负值表示改善)
                if not np.isnan(g_stats['HE']['mean']):
                    pooled_sd_he = np.sqrt((d_stats['HE']['std']**2 + g_stats['HE']['std']**2) / 2)
                    he_d = -(g_stats['HE']['mean'] - d_stats['HE']['mean']) / pooled_sd_he
                else:
                    he_d = dai_d
                
                # 加权综合SMD
                smd_total = (self.weights['DAI'] * dai_d + 
                           self.weights['Colon'] * colon_d + 
                           self.weights['HE'] * he_d)
                
                self.smd_scores[group] = {
                    'DAI_d': dai_d,
                    'Colon_d': colon_d,
                    'HE_d': he_d,
                    'SMD_total': smd_total
                }
            
            # 判断效应大小
            effect_size = self._interpret_effect_size(smd_total)
            
            results.append({
                '组别': group,
                'DAI_d': f"{self.smd_scores[group]['DAI_d']:.3f}" if group in self.smd_scores else '0.000',
                '结肠_d': f"{self.smd_scores[group]['Colon_d']:.3f}" if group in self.smd_scores else '0.000',
                'HE_d': f"{self.smd_scores[group]['HE_d']:.3f}" if group in self.smd_scores else '0.000',
                'SMD综合': f"{smd_total:.3f}",
                '效应大小': effect_size
            })
        
        df_results = pd.DataFrame(results)
        df_results = df_results.sort_values('SMD综合', ascending=False)
        
        return df_results
    
    def _interpret_effect_size(self, d: float) -> str:
        """解释Cohen's d效应大小"""
        abs_d = abs(d)
        if abs_d >= 0.8:
            return "大效应"
        elif abs_d >= 0.5:
            return "中等效应"
        elif abs_d >= 0.2:
            return "小效应"
        else:
            return "微弱效应"
    
    def statistical_analysis(self, groups_to_compare: List[str] = None, 
                           control_group: str = '5A'):
        """
        进行统计学分析（ANOVA + Dunnett's检验）
        
        Parameters:
        -----------
        groups_to_compare : List[str]
            需要比较的组别列表
        control_group : str
            对照组名称
        
        Returns:
        --------
        Dict : 统计分析结果
        """
        if self.data is None:
            print("请先加载数据")
            return None
        
        if groups_to_compare is None:
            groups_to_compare = self.data['group'].unique().tolist()
        
        results = {}
        
        for metric in ['DAI', '结肠长度', 'HE']:
            if metric not in self.data.columns:
                continue
            
            # 准备数据
            groups_data = []
            for group in groups_to_compare:
                group_data = self.data[self.data['group'] == group][metric].dropna()
                groups_data.append(group_data)
            
            # ANOVA
            f_stat, p_value = stats.f_oneway(*groups_data)
            
            results[metric] = {
                'ANOVA_F': f_stat,
                'ANOVA_p': p_value,
                'significant': p_value < 0.05
            }
            
            # 如果ANOVA显著，进行事后检验
            if p_value < 0.05:
                # 这里可以添加Dunnett's检验或其他多重比较
                pass
        
        return results
    
    def plot_results(self, save_path: str = None):
        """
        绘制结果图表
        
        Parameters:
        -----------
        save_path : str
            图片保存路径
        """
        if not self.rei_scores:
            print("请先计算REI评分")
            return
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        
        # 1. REI评分柱状图
        rei_data = [(k, v['REI']) for k, v in self.rei_scores.items() if v['REI'] is not None]
        rei_data.sort(key=lambda x: x[1], reverse=True)
        
        groups = [x[0] for x in rei_data]
        scores = [x[1] for x in rei_data]
        colors = ['green' if s > 100 else 'orange' if s >= 80 else 'blue' if s >= 50 else 'red' 
                 for s in scores]
        
        axes[0, 0].bar(groups, scores, color=colors, alpha=0.7)
        axes[0, 0].axhline(y=100, color='red', linestyle='--', label='金标准(100%)')
        axes[0, 0].set_title('相对疗效指数(REI)评分', fontsize=14, fontweight='bold')
        axes[0, 0].set_ylabel('REI (%)')
        axes[0, 0].set_xlabel('组别')
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3)
        
        # 2. 各指标改善率热图
        improvement_data = []
        for group in groups:
            if group in self.rei_scores:
                improvement_data.append([
                    self.rei_scores[group]['DAI_improvement'],
                    self.rei_scores[group]['Colon_improvement'],
                    self.rei_scores[group]['HE_improvement']
                ])
        
        im = axes[0, 1].imshow(np.array(improvement_data).T, cmap='RdYlGn', aspect='auto')
        axes[0, 1].set_xticks(range(len(groups)))
        axes[0, 1].set_xticklabels(groups, rotation=45)
        axes[0, 1].set_yticks(range(3))
        axes[0, 1].set_yticklabels(['DAI改善率', '结肠长度改善率', 'HE改善率'])
        axes[0, 1].set_title('各指标改善率热图', fontsize=14, fontweight='bold')
        plt.colorbar(im, ax=axes[0, 1])
        
        # 3. 原始数据箱线图 - DAI
        data_for_plot = []
        labels_for_plot = []
        for group in ['con', 'D', '5A', 'JZ'] + [g for g in groups if g not in ['con', 'D', '5A', 'JZ']]:
            if group in self.data['group'].values:
                data_for_plot.append(self.data[self.data['group'] == group]['DAI'].dropna())
                labels_for_plot.append(group)
        
        axes[1, 0].boxplot(data_for_plot, labels=labels_for_plot)
        axes[1, 0].set_title('DAI指数分布', fontsize=14, fontweight='bold')
        axes[1, 0].set_ylabel('DAI指数')
        axes[1, 0].set_xlabel('组别')
        axes[1, 0].grid(True, alpha=0.3)
        
        # 4. 原始数据箱线图 - 结肠长度
        data_for_plot = []
        labels_for_plot = []
        for group in ['con', 'D', '5A', 'JZ'] + [g for g in groups if g not in ['con', 'D', '5A', 'JZ']]:
            if group in self.data['group'].values:
                data_for_plot.append(self.data[self.data['group'] == group]['结肠长度'].dropna())
                labels_for_plot.append(group)
        
        axes[1, 1].boxplot(data_for_plot, labels=labels_for_plot)
        axes[1, 1].set_title('结肠长度分布', fontsize=14, fontweight='bold')
        axes[1, 1].set_ylabel('结肠长度 (cm)')
        axes[1, 1].set_xlabel('组别')
        axes[1, 1].grid(True, alpha=0.3)
        
        plt.suptitle('溃疡性结肠炎治疗效果综合分析', fontsize=16, fontweight='bold')
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"✓ 图表已保存至：{save_path}")
        
        # 不显示图表窗口，避免在无界面环境下出现问题
        plt.close()
    
    def export_results(self, output_path: str = 'results.csv'):
        """
        导出分析结果到CSV文件
        
        Parameters:
        -----------
        output_path : str
            输出文件路径
        """
        # 首先计算个体得分
        scored_data = self.calculate_individual_scores()
        if scored_data is None:
            return
        
        # 保存包含所有得分的完整数据
        full_output_path = output_path.replace('.csv', '_with_scores.csv')
        scored_data.to_csv(full_output_path, index=False, encoding='utf-8')
        print(f"✓ 完整数据已导出至：{full_output_path}")
        
        # 如果还需要Excel格式的汇总结果
        if output_path.endswith('.csv'):
            excel_path = output_path.replace('.csv', '.xlsx')
        else:
            excel_path = output_path + '.xlsx'
            
        with pd.ExcelWriter(excel_path, engine='openpyxl') as writer:
            # 完整得分数据
            scored_data.to_excel(writer, sheet_name='完整数据和得分', index=False)
            
            # 原始数据统计
            summary_df = self.calculate_rei()
            if summary_df is not None:
                summary_df.to_excel(writer, sheet_name='REI评分', index=False)
            
            # SMD评分
            smd_df = self.calculate_smd()
            if smd_df is not None:
                smd_df.to_excel(writer, sheet_name='SMD评分', index=False)
            
            # 推荐结论
            recommendations = self.get_recommendations()
            rec_df = pd.DataFrame(recommendations)
            rec_df.to_excel(writer, sheet_name='推荐结论', index=False)
            
            # 各组得分汇总统计
            score_summary = scored_data.groupby('group')[['DAI得分', '结肠长度得分', 'HE得分', '综合得分']].agg(['count', 'mean', 'std']).round(2)
            score_summary.to_excel(writer, sheet_name='得分汇总统计')
        
        print(f"✓ 汇总结果已导出至：{excel_path}")
        return full_output_path, excel_path
    
    def get_recommendations(self) -> List[Dict]:
        """获取推荐结论"""
        if not self.rei_scores:
            return []
        
        recommendations = []
        
        for group, scores in self.rei_scores.items():
            if scores['REI'] is None:
                continue
            
            rei = scores['REI']
            if rei > 100:
                category = "★★★ 优于金标准"
                recommendation = "优先推进深入研究"
            elif rei >= 80:
                category = "★★ 与金标准相当"
                recommendation = "值得进一步优化"
            elif rei >= 50:
                category = "★ 有效但弱于金标准"
                recommendation = "可作为备选方案"
            else:
                category = "效果不理想"
                recommendation = "需要结构改造或放弃"
            
            recommendations.append({
                '化合物': group,
                'REI(%)': f"{rei:.1f}",
                '效果分类': category,
                '研究建议': recommendation
            })
        
        return sorted(recommendations, key=lambda x: float(x['REI(%)'].replace('N/A', '0')), reverse=True)


# 使用示例
if __name__ == "__main__":
    # 初始化评分系统
    scorer = ColitisScoring()
    
    # 加载数据
    data_file = '03- 表型指标全数据_2.csv'
    try:
        scorer.data = pd.read_csv(data_file, encoding='utf-8')
        print(f"✓ 成功加载CSV数据：{len(scorer.data)}条记录")
        scorer._calculate_group_stats()
    except Exception as e:
        print(f"✗ 数据加载失败：{e}")
        exit(1)
    
    # 计算个体得分
    print("\n" + "="*60)
    print("计算个体得分和综合得分")
    print("="*60)
    scored_data = scorer.calculate_individual_scores()
    if scored_data is not None:
        print(f"样本得分范围：")
        print(f"DAI得分: {scored_data['DAI得分'].min():.1f} - {scored_data['DAI得分'].max():.1f}")
        print(f"结肠长度得分: {scored_data['结肠长度得分'].min():.1f} - {scored_data['结肠长度得分'].max():.1f}")
        print(f"HE得分: {scored_data['HE得分'].min():.1f} - {scored_data['HE得分'].max():.1f}")
        print(f"综合得分: {scored_data['综合得分'].min():.1f} - {scored_data['综合得分'].max():.1f}")
    
    
    # 计算REI评分
    print("\n" + "="*60)
    print("相对疗效指数(REI)评分结果")
    print("="*60)
    rei_results = scorer.calculate_rei()
    print(rei_results.to_string(index=False))
    
    # 计算SMD评分
    print("\n" + "="*60)
    print("标准化均值差(SMD)评分结果")
    print("="*60)
    smd_results = scorer.calculate_smd()
    print(smd_results.to_string(index=False))
    
    # 获取推荐结论
    print("\n" + "="*60)
    print("研究推荐结论")
    print("="*60)
    recommendations = scorer.get_recommendations()
    for rec in recommendations[:5]:  # 显示前5个
        print(f"{rec['化合物']}: {rec['效果分类']} - {rec['研究建议']}")
    
    # 绘制图表
    scorer.plot_results('analysis_results.png')
    
    # 导出结果
    print("\n" + "="*60)
    print("导出分析结果")
    print("="*60)
    csv_file, excel_file = scorer.export_results('colitis_analysis_results.csv')
    
    print("\n✓ 分析完成！")