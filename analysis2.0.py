#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
金芝及其单体成分治疗溃疡性结肠炎综合评分系统
Integrated Scoring System for JinZhi and Its Monomers in UC Treatment

整合了REI、<PERSON>'s d、Z-score和CCSI四种评分方法
"""

import pandas as pd
import numpy as np
from scipy import stats
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, List, Tuple
import warnings
warnings.filterwarnings('ignore')

# 设置中文显示 - 使用macOS系统字体
plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class IntegratedColitisScoring:
    """整合多种评分方法的综合系统"""
    
    def __init__(self, data_path: str = None):
        """初始化综合评分系统"""
        self.data = None
        self.group_stats = {}
        
        # 固定组别（每批次都有）
        self.fixed_groups = ['Con', 'D', '5A', 'JZ']
        
        # 评分结果存储
        self.scores = {
            'REI': {},     # 相对疗效指数
            'Cohen_d': {}, # 效应量
            'Z_score': {}, # Z分数
            'CCSI': {}     # 综合评分
        }
        
        # 权重设置（使用等权重）
        self.weights = {
            'DAI': 0.333,
            'Colon': 0.333,
            'HE': 0.334
        }
        
        if data_path:
            self.load_data(data_path)
    
    def load_data(self, filepath: str):
        """加载数据"""
        try:
            # 根据文件扩展名选择读取方法
            if filepath.endswith('.csv'):
                self.data = pd.read_csv(filepath, encoding='utf-8')
            else:
                self.data = pd.read_excel(filepath, sheet_name=0)
            
            # 统一组名
            self.data['group'] = self.data['group'].replace({
                'con': 'Con', 'D': 'D', '5A': '5A', 'JZ': 'JZ'
            })
            print(f"✓ 成功加载数据：{len(self.data)}条记录")
            self._calculate_group_statistics()
        except Exception as e:
            print(f"✗ 数据加载失败：{e}")
    
    def _calculate_group_statistics(self):
        """计算各组统计量"""
        groups = self.data['group'].unique()
        
        for group in groups:
            group_data = self.data[self.data['group'] == group]
            stats = {}
            
            for metric in ['DAI', '结肠长度', 'HE']:
                if metric in group_data.columns:
                    values = group_data[metric].dropna()
                    if len(values) > 0:
                        stats[metric] = {
                            'mean': values.mean(),
                            'std': values.std(),
                            'sem': values.sem(),
                            'n': len(values),
                            'values': values.tolist()
                        }
            
            self.group_stats[group] = stats
    
    def calculate_all_scores(self):
        """计算所有评分方法"""
        if not self.group_stats:
            print("请先加载数据")
            return None
        
        # 获取基准组数据
        con_stats = self.group_stats.get('Con', {})
        d_stats = self.group_stats.get('D', {})
        jz_stats = self.group_stats.get('JZ', {})  # 改为JZ作为基准组
        
        # 计算所有测试组的评分
        test_groups = [g for g in self.group_stats.keys() 
                      if g not in ['Con', 'D']]
        
        results = []
        
        for group in test_groups:
            g_stats = self.group_stats[group]
            
            # 1. 计算REI（相对疗效指数，以JZ为基准）
            rei = self._calculate_rei(g_stats, d_stats, jz_stats)
            
            # 2. 计算Cohen's d
            cohen_d = self._calculate_cohens_d(g_stats, d_stats)
            
            # 3. 计算Z-score
            z_score = self._calculate_z_score(g_stats)
            
            # 4. 计算CCSI
            ccsi = self._calculate_ccsi(g_stats, d_stats, con_stats)
            
            # 判断是否为金芝单体成分
            is_monomer = group.startswith('Meb')
            
            # 整合结果
            results.append({
                '组别': group,
                '类型': '基准复方' if group == 'JZ' else '阳性药' if group == '5A' else '单体成分',
                
                # 原始数据
                'DAI': f"{g_stats['DAI']['mean']:.2f}±{g_stats['DAI']['sem']:.2f}",
                '结肠长度': f"{g_stats['结肠长度']['mean']:.2f}±{g_stats['结肠长度']['sem']:.2f}",
                'HE': f"{g_stats['HE']['mean']:.2f}±{g_stats['HE']['sem']:.2f}" 
                      if 'HE' in g_stats else 'N/A',
                
                # 四种评分
                'REI(%)': f"{rei['total']:.1f}",
                'Cohen_d': f"{cohen_d['average']:.2f}",
                'Z_score': f"{z_score['total']:.3f}",
                'CCSI': f"{ccsi:.3f}",
                
                # 效果分级
                'REI等级': self._grade_rei(rei['total']),
                'd效应': self._grade_cohen_d(cohen_d['average']),
                
                # 与金芝比较（仅单体成分）
                '优于金芝': self._compare_with_jz(rei['total']) if is_monomer else '-'
            })
        
        df_results = pd.DataFrame(results)
        
        # 按REI排序
        df_results['REI_numeric'] = df_results['REI(%)'].astype(float)
        df_results = df_results.sort_values('REI_numeric', ascending=False)
        df_results = df_results.drop('REI_numeric', axis=1)
        
        return df_results
    
    def _calculate_rei(self, g_stats, d_stats, asa_stats):
        """计算相对疗效指数（以JZ为100%基准）"""
        rei = {}
        
        # DAI
        if all([stat.get('DAI') for stat in [g_stats, d_stats, asa_stats]]):
            rei['DAI'] = ((d_stats['DAI']['mean'] - g_stats['DAI']['mean']) /
                         (d_stats['DAI']['mean'] - asa_stats['DAI']['mean']) * 100)
        else:
            rei['DAI'] = 0
        
        # 结肠长度
        if all([stat.get('结肠长度') for stat in [g_stats, d_stats, asa_stats]]):
            rei['Colon'] = ((g_stats['结肠长度']['mean'] - d_stats['结肠长度']['mean']) /
                           (asa_stats['结肠长度']['mean'] - d_stats['结肠长度']['mean']) * 100)
        else:
            rei['Colon'] = 0
        
        # HE
        if all([stat.get('HE') for stat in [g_stats, d_stats, asa_stats]]):
            rei['HE'] = ((d_stats['HE']['mean'] - g_stats['HE']['mean']) /
                        (d_stats['HE']['mean'] - asa_stats['HE']['mean']) * 100)
        else:
            rei['HE'] = rei['DAI']  # 如无HE数据，用DAI替代
        
        # 加权综合
        rei['total'] = (self.weights['DAI'] * rei['DAI'] +
                       self.weights['Colon'] * rei['Colon'] +
                       self.weights['HE'] * rei['HE'])
        
        return rei
    
    def _calculate_cohens_d(self, g_stats, d_stats):
        """
        计算Cohen's d效应量
        
        Cohen's d = (均值1 - 均值2) / 合并标准差
        
        解释：
        - d = 0.2: 小效应
        - d = 0.5: 中等效应  
        - d = 0.8: 大效应
        - d > 1.0: 非常大效应
        """
        cohen_d = {}
        
        # DAI (改善方向：降低)
        if g_stats.get('DAI') and d_stats.get('DAI'):
            pooled_sd = np.sqrt(
                ((g_stats['DAI']['n'] - 1) * g_stats['DAI']['std']**2 +
                 (d_stats['DAI']['n'] - 1) * d_stats['DAI']['std']**2) /
                (g_stats['DAI']['n'] + d_stats['DAI']['n'] - 2)
            )
            if pooled_sd > 0:
                cohen_d['DAI'] = (d_stats['DAI']['mean'] - g_stats['DAI']['mean']) / pooled_sd
            else:
                cohen_d['DAI'] = 0
        
        # 结肠长度 (改善方向：增加)
        if g_stats.get('结肠长度') and d_stats.get('结肠长度'):
            pooled_sd = np.sqrt(
                ((g_stats['结肠长度']['n'] - 1) * g_stats['结肠长度']['std']**2 +
                 (d_stats['结肠长度']['n'] - 1) * d_stats['结肠长度']['std']**2) /
                (g_stats['结肠长度']['n'] + d_stats['结肠长度']['n'] - 2)
            )
            if pooled_sd > 0:
                cohen_d['Colon'] = (g_stats['结肠长度']['mean'] - d_stats['结肠长度']['mean']) / pooled_sd
            else:
                cohen_d['Colon'] = 0
        
        # HE (改善方向：降低)
        if g_stats.get('HE') and d_stats.get('HE'):
            pooled_sd = np.sqrt(
                ((g_stats['HE']['n'] - 1) * g_stats['HE']['std']**2 +
                 (d_stats['HE']['n'] - 1) * d_stats['HE']['std']**2) /
                (g_stats['HE']['n'] + d_stats['HE']['n'] - 2)
            )
            if pooled_sd > 0:
                cohen_d['HE'] = (d_stats['HE']['mean'] - g_stats['HE']['mean']) / pooled_sd
            else:
                cohen_d['HE'] = 0
        
        # 计算平均效应量
        valid_d = [abs(v) for v in cohen_d.values() if v != 0]
        cohen_d['average'] = np.mean(valid_d) if valid_d else 0
        
        return cohen_d
    
    def _calculate_z_score(self, g_stats):
        """
        计算Z-score标准化评分
        
        Z = (X - μ) / σ
        其中μ和σ是所有组的总体均值和标准差
        """
        z_scores = {}
        
        # 计算所有组的总体统计量
        all_dai = []
        all_colon = []
        all_he = []
        
        for group_stat in self.group_stats.values():
            if group_stat.get('DAI'):
                all_dai.extend(group_stat['DAI']['values'])
            if group_stat.get('结肠长度'):
                all_colon.extend(group_stat['结肠长度']['values'])
            if group_stat.get('HE'):
                all_he.extend(group_stat['HE']['values'])
        
        # 计算总体均值和标准差
        dai_mean, dai_std = np.mean(all_dai), np.std(all_dai)
        colon_mean, colon_std = np.mean(all_colon), np.std(all_colon)
        he_mean, he_std = np.mean(all_he), np.std(all_he) if all_he else (0, 1)
        
        # 计算Z分数
        if g_stats.get('DAI') and dai_std > 0:
            z_scores['DAI'] = (g_stats['DAI']['mean'] - dai_mean) / dai_std
        else:
            z_scores['DAI'] = 0
        
        if g_stats.get('结肠长度') and colon_std > 0:
            # 结肠长度越大越好，所以取反
            z_scores['Colon'] = -(g_stats['结肠长度']['mean'] - colon_mean) / colon_std
        else:
            z_scores['Colon'] = 0
        
        if g_stats.get('HE') and he_std > 0:
            z_scores['HE'] = (g_stats['HE']['mean'] - he_mean) / he_std
        else:
            z_scores['HE'] = z_scores['DAI']
        
        # 加权综合Z分数（越小越好）
        z_scores['total'] = (self.weights['DAI'] * z_scores['DAI'] +
                            self.weights['Colon'] * z_scores['Colon'] +
                            self.weights['HE'] * z_scores['HE'])
        
        return z_scores
    
    def _calculate_ccsi(self, g_stats, d_stats, con_stats):
        """
        计算CCSI (Colitis Composite Severity Index)
        
        采用0-10标准化，然后加权求和
        """
        ccsi_components = {}
        
        # DAI标准化 (0-10)
        if g_stats.get('DAI') and d_stats.get('DAI') and con_stats.get('DAI'):
            dai_range = d_stats['DAI']['mean'] - con_stats['DAI']['mean']
            if dai_range > 0:
                ccsi_components['DAI'] = 10 * (g_stats['DAI']['mean'] - con_stats['DAI']['mean']) / dai_range
            else:
                ccsi_components['DAI'] = 0
        
        # 结肠长度标准化 (0-10，反向)
        if g_stats.get('结肠长度') and d_stats.get('结肠长度') and con_stats.get('结肠长度'):
            colon_range = con_stats['结肠长度']['mean'] - d_stats['结肠长度']['mean']
            if colon_range > 0:
                # 结肠缩短率
                shortening = (con_stats['结肠长度']['mean'] - g_stats['结肠长度']['mean']) / colon_range
                ccsi_components['Colon'] = 10 * shortening
            else:
                ccsi_components['Colon'] = 0
        
        # HE标准化 (0-10)
        if g_stats.get('HE') and d_stats.get('HE') and con_stats.get('HE'):
            he_range = d_stats['HE']['mean'] - con_stats['HE']['mean']
            if he_range > 0:
                ccsi_components['HE'] = 10 * (g_stats['HE']['mean'] - con_stats['HE']['mean']) / he_range
            else:
                ccsi_components['HE'] = 0
        
        # 加权综合CCSI (0-10分，越低越好)
        ccsi = (self.weights['DAI'] * ccsi_components.get('DAI', 0) +
                self.weights['Colon'] * ccsi_components.get('Colon', 0) +
                self.weights['HE'] * ccsi_components.get('HE', 0))
        
        return ccsi
    
    def _grade_rei(self, rei):
        """REI效果分级（以JZ为基准）"""
        if rei > 100:
            return '★★★ 优于JZ'
        elif rei >= 80:
            return '★★ 与JZ相当'
        elif rei >= 50:
            return '★ 有效'
        else:
            return '效果不佳'
    
    def _grade_cohen_d(self, d):
        """Cohen's d效应分级"""
        d_abs = abs(d)
        if d_abs >= 1.0:
            return '非常大'
        elif d_abs >= 0.8:
            return '大'
        elif d_abs >= 0.5:
            return '中等'
        elif d_abs >= 0.2:
            return '小'
        else:
            return '微弱'
    
    def _compare_with_jz(self, monomer_rei):
        """比较单体与金芝复方"""
        if 'JZ' in self.group_stats:
            jz_stats = self.group_stats['JZ']
            jz_rei = self._calculate_rei(
                jz_stats,
                self.group_stats.get('D', {}),
                self.group_stats.get('5A', {})
            )
            if monomer_rei > jz_rei['total']:
                return '是 ✓'
            else:
                return '否'
        return '-'
    
    def analyze_synergy(self):
        """分析金芝复方的协同效应"""
        if 'JZ' not in self.group_stats:
            print("缺少金芝数据")
            return None
        
        # 获取金芝和其单体成分
        jz_stats = self.group_stats['JZ']
        monomers = [g for g in self.group_stats.keys() if g.startswith('Meb')]
        
        if not monomers:
            print("缺少单体成分数据")
            return None
        
        # 计算金芝的REI
        jz_rei = self._calculate_rei(
            jz_stats,
            self.group_stats.get('D', {}),
            self.group_stats.get('5A', {})
        )
        
        # 计算所有单体的REI
        monomer_reis = []
        for monomer in monomers:
            m_rei = self._calculate_rei(
                self.group_stats[monomer],
                self.group_stats.get('D', {}),
                self.group_stats.get('5A', {})
            )
            monomer_reis.append({
                'name': monomer,
                'rei': m_rei['total']
            })
        
        # 找出最佳单体
        best_monomer = max(monomer_reis, key=lambda x: x['rei'])
        
        # 计算协同指数
        synergy_index = jz_rei['total'] / best_monomer['rei'] if best_monomer['rei'] > 0 else 0
        
        print("\n" + "="*60)
        print("金芝复方协同效应分析")
        print("="*60)
        print(f"\n金芝复方REI: {jz_rei['total']:.1f}%")
        print(f"最佳单体({best_monomer['name']}): {best_monomer['rei']:.1f}%")
        print(f"\n协同指数: {synergy_index:.2f}")
        
        if synergy_index > 1.1:
            print("结论: 存在协同增效作用 ✓")
        elif synergy_index > 0.9:
            print("结论: 效果相当")
        else:
            print("结论: 单体效果更好")
        
        # 详细比较
        print("\n各单体成分效果：")
        for m in sorted(monomer_reis, key=lambda x: x['rei'], reverse=True):
            print(f"  {m['name']}: {m['rei']:.1f}%")
        
        return {
            'jz_rei': jz_rei['total'],
            'best_monomer': best_monomer,
            'synergy_index': synergy_index,
            'all_monomers': monomer_reis
        }
    
    def plot_comprehensive_comparison(self, save_path: str = None):
        """绘制综合比较图"""
        if not self.group_stats:
            return
        
        # 准备数据
        groups = []
        rei_scores = []
        cohen_d_scores = []
        z_scores = []
        ccsi_scores = []
        
        test_groups = [g for g in self.group_stats.keys() if g not in ['Con', 'D']]
        
        for group in test_groups:
            g_stats = self.group_stats[group]
            d_stats = self.group_stats.get('D', {})
            asa_stats = self.group_stats.get('5A', {})
            con_stats = self.group_stats.get('Con', {})
            
            groups.append(group)
            
            # 计算各种评分
            rei = self._calculate_rei(g_stats, d_stats, asa_stats)
            cohen_d = self._calculate_cohens_d(g_stats, d_stats)
            z_score = self._calculate_z_score(g_stats)
            ccsi = self._calculate_ccsi(g_stats, d_stats, con_stats)
            
            rei_scores.append(rei['total'])
            cohen_d_scores.append(cohen_d['average'])
            z_scores.append(-z_score['total'])  # 取反使得越高越好
            ccsi_scores.append(10 - ccsi)  # 反转使得越高越好
        
        # 创建图表
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        
        # 1. REI评分柱状图
        x_pos = np.arange(len(groups))
        colors = ['red' if g == '5A' else 'green' if g == 'JZ' else 'blue' 
                 for g in groups]
        
        axes[0, 0].bar(x_pos, rei_scores, color=colors, alpha=0.7)
        axes[0, 0].axhline(y=100, color='red', linestyle='--', label='JZ基准')
        axes[0, 0].set_xticks(x_pos)
        axes[0, 0].set_xticklabels(groups, rotation=45)
        axes[0, 0].set_ylabel('REI (%)')
        axes[0, 0].set_title('相对疗效指数 (REI)')
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3)
        
        # 2. Cohen's d效应量
        axes[0, 1].bar(x_pos, cohen_d_scores, color=colors, alpha=0.7)
        axes[0, 1].axhline(y=0.8, color='orange', linestyle='--', label='大效应阈值')
        axes[0, 1].set_xticks(x_pos)
        axes[0, 1].set_xticklabels(groups, rotation=45)
        axes[0, 1].set_ylabel("Cohen's d")
        axes[0, 1].set_title("Cohen's d 效应量")
        axes[0, 1].legend()
        axes[0, 1].grid(True, alpha=0.3)
        
        # 3. Z-score标准化评分
        axes[1, 0].bar(x_pos, z_scores, color=colors, alpha=0.7)
        axes[1, 0].set_xticks(x_pos)
        axes[1, 0].set_xticklabels(groups, rotation=45)
        axes[1, 0].set_ylabel('Z-score (反转)')
        axes[1, 0].set_title('Z-score 标准化评分')
        axes[1, 0].grid(True, alpha=0.3)
        
        # 4. CCSI综合评分
        axes[1, 1].bar(x_pos, ccsi_scores, color=colors, alpha=0.7)
        axes[1, 1].set_xticks(x_pos)
        axes[1, 1].set_xticklabels(groups, rotation=45)
        axes[1, 1].set_ylabel('CCSI (10-原始分)')
        axes[1, 1].set_title('CCSI 综合评分')
        axes[1, 1].grid(True, alpha=0.3)
        
        # 添加图例说明
        fig.text(0.5, 0.02, '红色: 5A (阳性药)  绿色: JZ (基准复方)  蓝色: 单体成分', 
                ha='center', fontsize=10)
        
        plt.suptitle('金芝及其单体成分治疗效果四维度评价', fontsize=16, fontweight='bold')
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"✓ 图表已保存至：{save_path}")
        
        plt.show()
    
    def export_results(self, output_path: str = 'jinzhi_analysis.xlsx'):
        """导出完整分析结果"""
        
        # 根据文件扩展名决定导出方式
        if output_path.endswith('.csv'):
            # CSV格式导出
            scores_df = self.calculate_all_scores()
            if scores_df is not None:
                scores_df.to_csv(output_path, index=False, encoding='utf-8')
                print(f"✓ 综合评分结果已导出至：{output_path}")
            else:
                print("✗ 无法生成综合评分数据")
        else:
            # Excel格式导出
            if not output_path.endswith(('.xlsx', '.xls')):
                output_path += '.xlsx'
                
            with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
                
                # 1. 综合评分表
                scores_df = self.calculate_all_scores()
                if scores_df is not None:
                    scores_df.to_excel(writer, sheet_name='综合评分', index=False)
                
                # 2. 原始统计数据
                stats_data = []
                for group, stats in self.group_stats.items():
                    row = {'组别': group}
                    for metric in ['DAI', '结肠长度', 'HE']:
                        if metric in stats:
                            row[f'{metric}_均值'] = stats[metric]['mean']
                            row[f'{metric}_标准差'] = stats[metric]['std']
                            row[f'{metric}_标准误'] = stats[metric]['sem']
                            row[f'{metric}_n'] = stats[metric]['n']
                    stats_data.append(row)
                
                stats_df = pd.DataFrame(stats_data)
                stats_df.to_excel(writer, sheet_name='统计描述', index=False)
                
                # 3. 评分方法说明
                methods_explanation = pd.DataFrame([
                    {'方法': 'REI', '全称': 'Relative Efficacy Index', 
                     '说明': '以JZ为100%基准的相对疗效', '范围': '0-200%', 
                     '解释': '>100%优于JZ'},
                    {'方法': "Cohen's d", '全称': "Cohen's d effect size", 
                     '说明': '标准化效应量', '范围': '0-∞', 
                     '解释': '0.2小/0.5中/0.8大/>1非常大'},
                    {'方法': 'Z-score', '全称': 'Z-score standardization', 
                     '说明': '标准化评分', '范围': '-3到+3', 
                     '解释': '越小越好(负值表示优于平均)'},
                    {'方法': 'CCSI', '全称': 'Composite Colitis Severity Index', 
                     '说明': '综合严重度指数', '范围': '0-10', 
                     '解释': '越小越好'}
                ])
                methods_explanation.to_excel(writer, sheet_name='方法说明', index=False)
            
            print(f"✓ Excel结果已导出至：{output_path}")


# 使用示例
if __name__ == "__main__":
    # 初始化综合评分系统
    scorer = IntegratedColitisScoring()
    
    # 加载数据
    scorer.load_data('03- 表型指标全数据_2.csv')
    
    # 计算所有评分
    print("\n" + "="*80)
    print("金芝及其单体成分综合评分结果")
    print("="*80)
    all_scores = scorer.calculate_all_scores()
    if all_scores is not None:
        print(all_scores.to_string(index=False))
    
    # 分析协同效应
    synergy_results = scorer.analyze_synergy()
    
    # 绘制综合比较图
    scorer.plot_comprehensive_comparison('comprehensive_comparison.png')
    
    # 导出结果
    scorer.export_results('jinzhi_comprehensive_analysis.csv')
    
    print("\n✓ 分析完成！")
    
    # 输出关键发现
    print("\n" + "="*80)
    print("关键发现")
    print("="*80)
    
    if all_scores is not None:
        # 找出最佳单体
        monomers = all_scores[all_scores['类型'] == '单体成分']
        if not monomers.empty:
            best_monomer = monomers.iloc[0]
            print(f"最佳单体成分: {best_monomer['组别']} (REI={best_monomer['REI(%)']}%)")
        
        # 评价最佳单体相对于JZ基准
        jz_row = all_scores[all_scores['组别'] == 'JZ']
        if not jz_row.empty and not monomers.empty:
            jz_rei = 100.0  # JZ作为基准固定为100%
            best_rei = float(best_monomer['REI(%)'])
            print(f"JZ复方作为基准 (REI={jz_rei:.1f}%)")
            if best_rei > 100:
                print(f"最佳单体优于JZ基准 (REI={best_rei:.1f}%)")
            else:
                print(f"最佳单体弱于JZ基准 (REI={best_rei:.1f}%)")