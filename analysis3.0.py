#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
金芝复方活性成分筛选评分系统
JinZhi Formula Active Component Screening System

实验设计：
- 固定组（每批次必有）：Con、D、5A、JZ
- 变动组（筛选目标）：不同批次的金芝单体成分

主要功能：
1. 多批次数据整合分析
2. 单体成分效果排序
3. 复方vs单体协同效应评估
4. 批次间标准化校正
"""

import pandas as pd
import numpy as np
from scipy import stats
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, List, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

# 设置中文显示 - 使用macOS系统字体
plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class JinZhiScreeningSystem:
    """金芝复方活性成分筛选系统"""
    
    def __init__(self):
        """初始化筛选系统"""
        # 固定组（标准组）- 每批次都有
        self.standard_groups = {
            'Con': '正常对照',
            'D': '疾病模型', 
            '5A': '阳性药(5-ASA)',
            'JZ': '金芝复方'
        }
        
        # 存储所有批次数据
        self.batch_data = {}
        self.all_monomers = []  # 所有测试过的单体
        
        # 评分权重（等权重）
        self.weights = {
            'DAI': 0.333,
            'Colon': 0.333,
            'HE': 0.334
        }
        
    def load_batch_data(self, filepath: str, batch_name: str):
        """
        加载一个批次的数据
        
        Parameters:
        -----------
        filepath : str
            数据文件路径
        batch_name : str
            批次名称（如'Batch1', 'Batch2'等）
        """
        try:
            # 根据文件扩展名选择读取方法
            if filepath.endswith('.csv'):
                data = pd.read_csv(filepath, encoding='utf-8')
            else:
                data = pd.read_excel(filepath, sheet_name=0)
            
            # 标准化组名
            data['group'] = data['group'].replace({
                'con': 'Con', 'D': 'D', '5A': '5A', 'JZ': 'JZ'
            })
            
            # 识别本批次的单体成分
            all_groups = data['group'].unique()
            monomers = [g for g in all_groups if g not in self.standard_groups]
            
            # 存储批次数据
            self.batch_data[batch_name] = {
                'data': data,
                'monomers': monomers,
                'stats': self._calculate_batch_statistics(data)
            }
            
            # 更新单体列表
            self.all_monomers.extend(monomers)
            self.all_monomers = list(set(self.all_monomers))  # 去重
            
            print(f"✓ 成功加载批次 {batch_name}")
            print(f"  - 样本数: {len(data)}")
            print(f"  - 标准组: {', '.join(self.standard_groups.keys())}")
            print(f"  - 本批次单体: {', '.join(monomers)}")
            
        except Exception as e:
            print(f"✗ 批次 {batch_name} 加载失败: {e}")
    
    def _calculate_batch_statistics(self, data: pd.DataFrame) -> Dict:
        """计算批次内各组统计量"""
        stats = {}
        groups = data['group'].unique()
        
        for group in groups:
            group_data = data[data['group'] == group]
            group_stats = {}
            
            for metric in ['DAI', '结肠长度', 'HE']:
                if metric in group_data.columns:
                    values = group_data[metric].dropna()
                    if len(values) > 0:
                        group_stats[metric] = {
                            'mean': values.mean(),
                            'std': values.std(),
                            'sem': values.sem(),
                            'n': len(values),
                            'ci95_lower': values.mean() - 1.96 * values.sem(),
                            'ci95_upper': values.mean() + 1.96 * values.sem()
                        }
            
            stats[group] = group_stats
        
        return stats
    
    def calculate_batch_correction_factors(self) -> Dict:
        """
        计算批次校正因子
        使用5-ASA作为批次间的标准参照
        """
        if not self.batch_data:
            print("请先加载数据")
            return None
        
        correction_factors = {}
        
        # 计算所有批次5-ASA的平均效果
        all_5a_dai = []
        all_5a_colon = []
        all_5a_he = []
        
        for batch_name, batch_info in self.batch_data.items():
            stats = batch_info['stats']
            if '5A' in stats:
                if 'DAI' in stats['5A']:
                    all_5a_dai.append(stats['5A']['DAI']['mean'])
                if '结肠长度' in stats['5A']:
                    all_5a_colon.append(stats['5A']['结肠长度']['mean'])
                if 'HE' in stats['5A']:
                    all_5a_he.append(stats['5A']['HE']['mean'])
        
        # 计算总体均值
        overall_5a = {
            'DAI': np.mean(all_5a_dai) if all_5a_dai else None,
            'Colon': np.mean(all_5a_colon) if all_5a_colon else None,
            'HE': np.mean(all_5a_he) if all_5a_he else None
        }
        
        # 计算每个批次的校正因子
        for batch_name, batch_info in self.batch_data.items():
            stats = batch_info['stats']
            if '5A' in stats:
                factors = {}
                
                if 'DAI' in stats['5A'] and overall_5a['DAI']:
                    factors['DAI'] = overall_5a['DAI'] / stats['5A']['DAI']['mean']
                
                if '结肠长度' in stats['5A'] and overall_5a['Colon']:
                    factors['Colon'] = overall_5a['Colon'] / stats['5A']['结肠长度']['mean']
                
                if 'HE' in stats['5A'] and overall_5a['HE']:
                    factors['HE'] = overall_5a['HE'] / stats['5A']['HE']['mean']
                
                correction_factors[batch_name] = factors
        
        return correction_factors
    
    def screen_monomers(self, use_correction: bool = True) -> pd.DataFrame:
        """
        筛选所有单体成分的效果
        
        Parameters:
        -----------
        use_correction : bool
            是否使用批次校正
        
        Returns:
        --------
        DataFrame : 所有单体的评分排序
        """
        if not self.batch_data:
            print("请先加载数据")
            return None
        
        # 获取批次校正因子
        correction_factors = self.calculate_batch_correction_factors() if use_correction else None
        
        results = []
        
        # 遍历所有批次
        for batch_name, batch_info in self.batch_data.items():
            stats = batch_info['stats']
            monomers = batch_info['monomers']
            
            # 获取本批次的基准数据
            d_stats = stats.get('D', {})
            asa_stats = stats.get('5A', {})
            jz_stats = stats.get('JZ', {})
            
            # 计算每个单体的评分
            for monomer in monomers:
                if monomer not in stats:
                    continue
                
                m_stats = stats[monomer]
                
                # 应用批次校正
                if correction_factors and batch_name in correction_factors:
                    m_stats = self._apply_correction(m_stats, correction_factors[batch_name])
                
                # 计算各种评分
                rei = self._calculate_rei(m_stats, d_stats, asa_stats)
                cohen_d = self._calculate_cohens_d(m_stats, d_stats)
                z_score = self._calculate_z_score(m_stats, stats)  # 新增z-score计算
                ccsi = self._calculate_ccsi(m_stats)  # 新增CCSI计算
                comparison_with_jz = self._compare_with_jinzhi(m_stats, jz_stats, d_stats)
                
                results.append({
                    '单体名称': monomer,
                    '批次': batch_name,
                    
                    # 原始数据
                    'DAI': f"{m_stats.get('DAI', {}).get('mean', 0):.2f}" if 'DAI' in m_stats else 'N/A',
                    '结肠长度': f"{m_stats.get('结肠长度', {}).get('mean', 0):.2f}" if '结肠长度' in m_stats else 'N/A',
                    'HE': f"{m_stats.get('HE', {}).get('mean', 0):.2f}" if 'HE' in m_stats else 'N/A',
                    
                    # 评分
                    'REI(%)': rei['total'],
                    'Cohen_d': cohen_d['average'],
                    'Z_score': z_score['composite'],  # 新增z-score
                    'CCSI': ccsi,  # 新增CCSI
                    
                    # 与金芝比较
                    'vs金芝': comparison_with_jz,
                    
                    # 分级
                    '效果等级': self._grade_effect(rei['total'])
                })
        
        # 创建DataFrame并排序
        df_results = pd.DataFrame(results)
        df_results = df_results.sort_values('REI(%)', ascending=False)
        
        return df_results
    
    def _apply_correction(self, stats: Dict, factors: Dict) -> Dict:
        """应用批次校正因子"""
        corrected = {}
        for metric in stats:
            if metric in factors:
                corrected[metric] = {
                    'mean': stats[metric]['mean'] * factors[metric],
                    'std': stats[metric]['std'],
                    'sem': stats[metric]['sem'],
                    'n': stats[metric]['n']
                }
            else:
                corrected[metric] = stats[metric]
        return corrected
    
    def _calculate_rei(self, g_stats, d_stats, asa_stats):
        """计算REI（相对疗效指数）"""
        rei = {}
        
        # DAI
        if all([g_stats.get('DAI'), d_stats.get('DAI'), asa_stats.get('DAI')]):
            d_mean = d_stats['DAI']['mean']
            asa_mean = asa_stats['DAI']['mean']
            g_mean = g_stats['DAI']['mean']
            if d_mean != asa_mean:
                rei['DAI'] = ((d_mean - g_mean) / (d_mean - asa_mean)) * 100
            else:
                rei['DAI'] = 0
        else:
            rei['DAI'] = 0
        
        # 结肠长度
        if all([g_stats.get('结肠长度'), d_stats.get('结肠长度'), asa_stats.get('结肠长度')]):
            d_mean = d_stats['结肠长度']['mean']
            asa_mean = asa_stats['结肠长度']['mean']
            g_mean = g_stats['结肠长度']['mean']
            if asa_mean != d_mean:
                rei['Colon'] = ((g_mean - d_mean) / (asa_mean - d_mean)) * 100
            else:
                rei['Colon'] = 0
        else:
            rei['Colon'] = 0
        
        # HE
        if all([g_stats.get('HE'), d_stats.get('HE'), asa_stats.get('HE')]):
            d_mean = d_stats['HE']['mean']
            asa_mean = asa_stats['HE']['mean']
            g_mean = g_stats['HE']['mean']
            if d_mean != asa_mean:
                rei['HE'] = ((d_mean - g_mean) / (d_mean - asa_mean)) * 100
            else:
                rei['HE'] = 0
        else:
            rei['HE'] = rei['DAI']  # 用DAI替代
        
        # 加权综合
        rei['total'] = (self.weights['DAI'] * rei['DAI'] +
                       self.weights['Colon'] * rei['Colon'] +
                       self.weights['HE'] * rei['HE'])
        
        return rei
    
    def _calculate_cohens_d(self, g_stats, d_stats):
        """计算Cohen's d效应量"""
        cohen_d = {}
        
        for metric in ['DAI', '结肠长度', 'HE']:
            if g_stats.get(metric) and d_stats.get(metric):
                g_mean = g_stats[metric]['mean']
                g_std = g_stats[metric]['std']
                g_n = g_stats[metric]['n']
                
                d_mean = d_stats[metric]['mean']
                d_std = d_stats[metric]['std']
                d_n = d_stats[metric]['n']
                
                # 合并标准差
                pooled_sd = np.sqrt(((g_n - 1) * g_std**2 + (d_n - 1) * d_std**2) / 
                                   (g_n + d_n - 2))
                
                if pooled_sd > 0:
                    if metric == '结肠长度':
                        cohen_d[metric] = (g_mean - d_mean) / pooled_sd
                    else:
                        cohen_d[metric] = (d_mean - g_mean) / pooled_sd
                else:
                    cohen_d[metric] = 0
        
        # 平均效应量
        valid_d = [abs(v) for v in cohen_d.values() if v != 0]
        cohen_d['average'] = np.mean(valid_d) if valid_d else 0
        
        return cohen_d
    
    def _calculate_z_score(self, target_stats, all_group_stats):
        """
        计算z-score标准化得分
        相对于所有组别的标准化得分
        
        Parameters:
        -----------
        target_stats : dict
            目标组的统计数据
        all_group_stats : dict
            所有组别的统计数据
        
        Returns:
        --------
        dict : z-score结果
        """
        z_scores = {}
        
        # 收集所有组别的数据来计算总体均值和标准差
        for metric in ['DAI', '结肠长度', 'HE']:
            all_values = []
            
            # 收集所有组别的该指标数据
            for group_name, group_stats in all_group_stats.items():
                if metric in group_stats and 'mean' in group_stats[metric]:
                    all_values.append(group_stats[metric]['mean'])
            
            if len(all_values) > 1 and metric in target_stats:
                # 计算总体均值和标准差
                overall_mean = np.mean(all_values)
                overall_std = np.std(all_values, ddof=1)
                
                if overall_std > 0:
                    target_value = target_stats[metric]['mean']
                    # 对于DAI和HE，值越小越好（负z-score更好）
                    # 对于结肠长度，值越大越好（正z-score更好）
                    if metric == '结肠长度':
                        z_scores[metric] = (target_value - overall_mean) / overall_std
                    else:
                        z_scores[metric] = -(target_value - overall_mean) / overall_std
                else:
                    z_scores[metric] = 0
            else:
                z_scores[metric] = 0
        
        # 计算综合z-score（等权重平均）
        valid_scores = [v for v in z_scores.values() if v != 0]
        z_scores['composite'] = np.mean(valid_scores) if valid_scores else 0
        
        return z_scores
    
    def _calculate_ccsi(self, target_stats):
        """
        计算综合结肠炎严重度指数 (Composite Colitis Severity Index)
        基于DAI、结肠长度、HE的综合评分，范围0-10，值越小表示炎症越轻
        
        Parameters:
        -----------
        target_stats : dict
            目标组的统计数据
        
        Returns:
        --------
        float : CCSI得分
        """
        # 获取各指标值
        dai = target_stats.get('DAI', {}).get('mean', 0)
        colon_length = target_stats.get('结肠长度', {}).get('mean', 8.5)  # 正常值约8.5cm
        he = target_stats.get('HE', {}).get('mean', 0)
        
        # DAI标准化 (0-7范围 -> 0-3)
        dai_score = min(dai / 7.0 * 3, 3)
        
        # 结肠长度标准化 (长度越短得分越高，5-10cm范围 -> 0-3)
        # 正常约8.5cm，炎症时缩短至5-6cm
        colon_score = max(0, (8.5 - colon_length) / 3.5 * 3)
        colon_score = min(colon_score, 3)
        
        # HE标准化 (0-9范围 -> 0-4)  
        he_score = min(he / 9.0 * 4, 4)
        
        # 综合CCSI = DAI得分 + 结肠得分 + HE得分 (0-10范围)
        ccsi = dai_score + colon_score + he_score
        
        return round(ccsi, 3)
    
    def _compare_with_jinzhi(self, m_stats, jz_stats, d_stats):
        """比较单体与金芝复方的效果"""
        if not jz_stats or not d_stats:
            return '-'
        
        # 简单比较DAI的改善程度
        if all([m_stats.get('DAI'), jz_stats.get('DAI'), d_stats.get('DAI')]):
            m_improvement = d_stats['DAI']['mean'] - m_stats['DAI']['mean']
            jz_improvement = d_stats['DAI']['mean'] - jz_stats['DAI']['mean']
            
            if m_improvement > jz_improvement * 1.1:
                return '优于金芝'
            elif m_improvement > jz_improvement * 0.9:
                return '相当'
            else:
                return '弱于金芝'
        
        return '-'
    
    def _grade_effect(self, rei):
        """效果分级"""
        if rei > 120:
            return '★★★★ 极优'
        elif rei > 100:
            return '★★★ 优秀'
        elif rei >= 80:
            return '★★ 良好'
        elif rei >= 50:
            return '★ 有效'
        else:
            return '效果差'
    
    def analyze_jinzhi_synergy(self) -> Dict:
        """
        分析金芝复方的协同效应
        比较复方与其最佳单体成分
        """
        if not self.batch_data:
            print("请先加载数据")
            return None
        
        synergy_results = {}
        
        for batch_name, batch_info in self.batch_data.items():
            stats = batch_info['stats']
            
            # 获取金芝和基准数据
            if 'JZ' not in stats or 'D' not in stats or '5A' not in stats:
                continue
            
            jz_stats = stats['JZ']
            d_stats = stats['D']
            asa_stats = stats['5A']
            
            # 计算金芝的REI
            jz_rei = self._calculate_rei(jz_stats, d_stats, asa_stats)
            
            # 计算本批次所有单体的REI
            monomer_reis = []
            for monomer in batch_info['monomers']:
                if monomer in stats:
                    m_rei = self._calculate_rei(stats[monomer], d_stats, asa_stats)
                    monomer_reis.append({
                        'name': monomer,
                        'rei': m_rei['total']
                    })
            
            if monomer_reis:
                # 找出最佳单体
                best_monomer = max(monomer_reis, key=lambda x: x['rei'])
                
                # 计算协同指数
                synergy_index = jz_rei['total'] / best_monomer['rei'] if best_monomer['rei'] > 0 else 0
                
                synergy_results[batch_name] = {
                    'jz_rei': jz_rei['total'],
                    'best_monomer': best_monomer,
                    'synergy_index': synergy_index,
                    'all_monomers': monomer_reis,
                    'conclusion': '协同增效' if synergy_index > 1.1 else '无明显协同'
                }
        
        return synergy_results
    
    def plot_screening_results(self, save_path: str = None):
        """绘制筛选结果图表"""
        # 获取所有单体评分
        df_results = self.screen_monomers()
        if df_results is None or df_results.empty:
            print("无数据可绘制")
            return
        
        # 创建图表
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        
        # 1. REI评分排序图
        ax1 = axes[0, 0]
        df_sorted = df_results.sort_values('REI(%)', ascending=True).tail(15)
        colors = ['green' if x > 100 else 'orange' if x > 80 else 'blue' 
                 for x in df_sorted['REI(%)']]
        ax1.barh(range(len(df_sorted)), df_sorted['REI(%)'], color=colors)
        ax1.set_yticks(range(len(df_sorted)))
        ax1.set_yticklabels(df_sorted['单体名称'])
        ax1.axvline(x=100, color='red', linestyle='--', label='5-ASA基准')
        ax1.set_xlabel('REI (%)')
        ax1.set_title('单体成分REI评分排序（TOP15）')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 2. Cohen's d效应量分布
        ax2 = axes[0, 1]
        ax2.scatter(df_results['REI(%)'], df_results['Cohen_d'], alpha=0.6, s=100)
        ax2.axhline(y=0.8, color='orange', linestyle='--', label='大效应阈值')
        ax2.axvline(x=100, color='red', linestyle='--', label='5-ASA基准')
        ax2.set_xlabel('REI (%)')
        ax2.set_ylabel("Cohen's d")
        ax2.set_title('REI vs Cohen\'s d 相关性')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        # 为点添加标签（TOP5）
        top5 = df_results.nlargest(5, 'REI(%)')
        for _, row in top5.iterrows():
            ax2.annotate(row['单体名称'], 
                        (row['REI(%)'], row['Cohen_d']),
                        xytext=(5, 5), textcoords='offset points',
                        fontsize=8, alpha=0.7)
        
        # 3. 批次间比较
        ax3 = axes[1, 0]
        batch_grouped = df_results.groupby('批次')['REI(%)'].agg(['mean', 'std'])
        x_pos = np.arange(len(batch_grouped))
        ax3.bar(x_pos, batch_grouped['mean'], yerr=batch_grouped['std'], 
               capsize=5, alpha=0.7)
        ax3.set_xticks(x_pos)
        ax3.set_xticklabels(batch_grouped.index, rotation=45)
        ax3.set_ylabel('平均REI (%)')
        ax3.set_title('批次间单体效果比较')
        ax3.grid(True, alpha=0.3)
        
        # 4. 与金芝比较
        ax4 = axes[1, 1]
        comparison_counts = df_results['vs金芝'].value_counts()
        colors_pie = ['green', 'orange', 'red', 'gray']
        ax4.pie(comparison_counts.values, labels=comparison_counts.index, 
               autopct='%1.1f%%', colors=colors_pie[:len(comparison_counts)])
        ax4.set_title('单体vs金芝复方效果比较')
        
        plt.suptitle('金芝复方活性成分筛选结果', fontsize=16, fontweight='bold')
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"✓ 图表已保存至：{save_path}")
        
        plt.show()
    
    def generate_screening_report(self, output_path: str = 'screening_report.xlsx'):
        """生成筛选报告"""
        
        # 根据文件扩展名决定导出方式
        if output_path.endswith('.csv'):
            # CSV格式导出 - 只导出主要筛选结果
            df_results = self.screen_monomers()
            if df_results is not None:
                df_results.to_csv(output_path, index=False, encoding='utf-8')
                print(f"✓ 筛选报告已导出至：{output_path}")
            else:
                print("✗ 无法生成筛选结果")
        else:
            # Excel格式导出 - 包含完整信息
            if not output_path.endswith(('.xlsx', '.xls')):
                output_path += '.xlsx'
                
            with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
                
                # 1. 单体筛选结果
                df_results = self.screen_monomers()
                if df_results is not None:
                    df_results.to_excel(writer, sheet_name='单体筛选结果', index=False)
                
                # 2. 批次校正因子
                factors = self.calculate_batch_correction_factors()
                if factors:
                    df_factors = pd.DataFrame(factors).T
                    df_factors.to_excel(writer, sheet_name='批次校正因子', index=True)
                
                # 3. 协同效应分析
                synergy = self.analyze_jinzhi_synergy()
                if synergy:
                    synergy_data = []
                    for batch, info in synergy.items():
                        synergy_data.append({
                            '批次': batch,
                            '金芝REI': info['jz_rei'],
                            '最佳单体': info['best_monomer']['name'],
                            '最佳单体REI': info['best_monomer']['rei'],
                            '协同指数': info['synergy_index'],
                            '结论': info['conclusion']
                        })
                    df_synergy = pd.DataFrame(synergy_data)
                    df_synergy.to_excel(writer, sheet_name='协同效应分析', index=False)
                
                # 4. TOP10单体汇总
                if df_results is not None and not df_results.empty:
                    top10 = df_results.nlargest(10, 'REI(%)')
                    top10.to_excel(writer, sheet_name='TOP10单体', index=False)
            
            print(f"✓ Excel筛选报告已生成：{output_path}")
    
    def print_summary(self):
        """打印筛选总结"""
        print("\n" + "="*80)
        print("金芝复方活性成分筛选总结")
        print("="*80)
        
        # 批次信息
        print(f"\n批次数量: {len(self.batch_data)}")
        print(f"测试单体总数: {len(self.all_monomers)}")
        
        # 获取筛选结果
        df_results = self.screen_monomers()
        if df_results is not None and not df_results.empty:
            # TOP5单体
            print("\n【TOP5活性单体】")
            top5 = df_results.nlargest(5, 'REI(%)')
            for idx, row in top5.iterrows():
                print(f"  {row['单体名称']}: REI={row['REI(%)']:.1f}%, "
                      f"Cohen's d={row['Cohen_d']:.2f}, {row['效果等级']}")
            
            # 优于金芝的单体
            better_than_jz = df_results[df_results['vs金芝'] == '优于金芝']
            if not better_than_jz.empty:
                print(f"\n【优于金芝复方的单体】")
                for _, row in better_than_jz.iterrows():
                    print(f"  {row['单体名称']} (批次{row['批次']})")
        
        # 协同效应分析
        synergy = self.analyze_jinzhi_synergy()
        if synergy:
            print("\n【协同效应分析】")
            for batch, info in synergy.items():
                print(f"  批次{batch}: 金芝REI={info['jz_rei']:.1f}%, "
                      f"最佳单体({info['best_monomer']['name']})={info['best_monomer']['rei']:.1f}%, "
                      f"{info['conclusion']}")


# 使用示例
if __name__ == "__main__":
    # 初始化筛选系统
    screener = JinZhiScreeningSystem()
    
    # 加载多批次数据
    # 批次1：测试Mebs1-7
    screener.load_batch_data('03- 表型指标全数据_2.csv', 'Batch1')
    
    # 批次2：测试Meb2和其他新单体
    # screener.load_batch_data('batch2_data.xlsx', 'Batch2')
    
    # 批次3：测试更多单体
    # screener.load_batch_data('batch3_data.xlsx', 'Batch3')
    
    # 筛选所有单体
    print("\n" + "="*80)
    print("单体成分筛选结果")
    print("="*80)
    results = screener.screen_monomers(use_correction=True)
    if results is not None:
        print(results.head(10).to_string(index=False))
    
    # 分析协同效应
    synergy = screener.analyze_jinzhi_synergy()
    
    # 绘制结果图表
    screener.plot_screening_results('screening_results.png')
    
    # 生成报告
    screener.generate_screening_report('jinzhi_screening_report.csv')
    
    # 打印总结
    screener.print_summary()
    
    print("\n✓ 筛选分析完成！")